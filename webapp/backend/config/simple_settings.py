"""
简化的应用配置设置
避免Pydantic版本兼容问题
"""

import os
from typing import List
from pathlib import Path

class Settings:
    """应用设置"""
    
    def __init__(self):
        # 基础配置
        self.APP_NAME = os.getenv("APP_NAME", "Coin-Analyze")
        self.VERSION = os.getenv("VERSION", "1.0.0")
        self.DEBUG = os.getenv("DEBUG", "true").lower() == "true"
        self.ENVIRONMENT = os.getenv("ENVIRONMENT", "development")
        
        # 服务器配置
        self.HOST = os.getenv("HOST", "0.0.0.0")
        self.PORT = int(os.getenv("PORT", "8000"))
        
        # CORS配置 - 确保包含3000端口
        allowed_origins_str = os.getenv("ALLOWED_ORIGINS", "http://localhost:3000,http://127.0.0.1:3000,http://************:3000,http://localhost:3001,http://127.0.0.1:3001,http://************:3001")
        self.ALLOWED_ORIGINS = [origin.strip() for origin in allowed_origins_str.split(",")]
        
        # 数据库配置
        self.DATABASE_URL = os.getenv("DATABASE_URL", "sqlite:///./coin_analyze.db")
        
        # Redis配置
        self.REDIS_URL = os.getenv("REDIS_URL", "redis://localhost:6379/0")
        self.REDIS_HOST = os.getenv("REDIS_HOST", "localhost")
        self.REDIS_PORT = int(os.getenv("REDIS_PORT", "6379"))
        self.REDIS_DB = int(os.getenv("REDIS_DB", "0"))
        
        # API密钥配置
        self.BINANCE_API_KEY = os.getenv("BINANCE_API_KEY", "")
        self.BINANCE_SECRET_KEY = os.getenv("BINANCE_SECRET_KEY", "")
        
        # 第三方API配置
        self.COINGECKO_API_KEY = os.getenv("COINGECKO_API_KEY", "")
        self.NEWSAPI_KEY = os.getenv("NEWSAPI_KEY", "")
        
        # JWT配置
        self.SECRET_KEY = os.getenv("SECRET_KEY", "your-secret-key-change-in-production")
        self.ALGORITHM = os.getenv("ALGORITHM", "HS256")
        self.ACCESS_TOKEN_EXPIRE_MINUTES = int(os.getenv("ACCESS_TOKEN_EXPIRE_MINUTES", "30"))
        
        # 文件路径配置
        self.PROJECT_ROOT = Path(__file__).parent.parent.parent.parent
        self.DATA_PATH = self.PROJECT_ROOT / "data"
        self.MODELS_PATH = self.PROJECT_ROOT / "models"
        self.LOGS_PATH = self.PROJECT_ROOT / "logs"
        
        # 预测模型配置
        self.MODEL_UPDATE_INTERVAL = int(os.getenv("MODEL_UPDATE_INTERVAL", "3600"))
        self.PREDICTION_CACHE_TTL = int(os.getenv("PREDICTION_CACHE_TTL", "300"))
        
        # 市场数据配置
        self.MARKET_DATA_UPDATE_INTERVAL = int(os.getenv("MARKET_DATA_UPDATE_INTERVAL", "5"))
        supported_symbols_str = os.getenv("SUPPORTED_SYMBOLS", "BTCUSDT,ETHUSDT,BNBUSDT,XRPUSDT,ADAUSDT,SOLUSDT,DOGEUSDT,AVAXUSDT,TRXUSDT,TONUSDT")
        self.SUPPORTED_SYMBOLS = [symbol.strip().upper() for symbol in supported_symbols_str.split(",")]
        
        # WebSocket配置
        self.WS_HEARTBEAT_INTERVAL = int(os.getenv("WS_HEARTBEAT_INTERVAL", "30"))
        self.WS_MAX_CONNECTIONS = int(os.getenv("WS_MAX_CONNECTIONS", "1000"))
        
        # 日志配置
        self.LOG_LEVEL = os.getenv("LOG_LEVEL", "INFO")
        self.LOG_FORMAT = os.getenv("LOG_FORMAT", "%(asctime)s - %(name)s - %(levelname)s - %(message)s")
        
        # 缓存配置
        self.CACHE_DEFAULT_TTL = int(os.getenv("CACHE_DEFAULT_TTL", "300"))
        self.CACHE_MARKET_DATA_TTL = int(os.getenv("CACHE_MARKET_DATA_TTL", "60"))
        self.CACHE_PREDICTION_TTL = int(os.getenv("CACHE_PREDICTION_TTL", "300"))
        
        # 限流配置
        self.RATE_LIMIT_PER_MINUTE = int(os.getenv("RATE_LIMIT_PER_MINUTE", "60"))
        self.RATE_LIMIT_BURST = int(os.getenv("RATE_LIMIT_BURST", "10"))
        
        # 安全配置
        self.ENABLE_HTTPS = os.getenv("ENABLE_HTTPS", "false").lower() == "true"
        self.SSL_CERT_PATH = os.getenv("SSL_CERT_PATH", "")
        self.SSL_KEY_PATH = os.getenv("SSL_KEY_PATH", "")
        
        # 确保必要的目录存在
        self._ensure_directories()
    
    def _ensure_directories(self):
        """确保必要的目录存在"""
        for path in [self.DATA_PATH, self.MODELS_PATH, self.LOGS_PATH]:
            path.mkdir(parents=True, exist_ok=True)
    
    @property
    def database_url_sync(self) -> str:
        """同步数据库URL"""
        return self.DATABASE_URL.replace("postgresql://", "postgresql+psycopg2://")
    
    @property
    def database_url_async(self) -> str:
        """异步数据库URL"""
        return self.DATABASE_URL.replace("postgresql://", "postgresql+asyncpg://")

# 创建全局设置实例
settings = Settings()

# 加载.env文件（如果存在）
try:
    from dotenv import load_dotenv
    env_file = Path(__file__).parent.parent.parent / ".env"
    if env_file.exists():
        load_dotenv(env_file)
        # 重新初始化设置以应用.env文件中的值
        settings = Settings()
except ImportError:
    pass
