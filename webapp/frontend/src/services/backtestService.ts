import { apiClient } from './api'
import type { 
  BacktestRequest, 
  BacktestStatus, 
  BacktestResult, 
  StrategyInfo,
  ApiResponse 
} from '../types'

/**
 * 回测服务
 * 提供与回测API的交互功能
 */
export class BacktestService {
  private static instance: BacktestService
  private baseUrl = '/api/v1/backtest'

  static getInstance(): BacktestService {
    if (!BacktestService.instance) {
      BacktestService.instance = new BacktestService()
    }
    return BacktestService.instance
  }

  /**
   * 启动回测任务
   */
  async startBacktest(request: BacktestRequest): Promise<{ task_id: string; message: string }> {
    try {
      console.log('🚀 启动回测任务:', request)
      const response = await apiClient.post(`${this.baseUrl}/start`, request)
      return response
    } catch (error) {
      console.error('❌ 启动回测失败:', error)
      throw new Error('启动回测失败，请检查参数设置')
    }
  }

  /**
   * 查询回测任务状态
   */
  async getBacktestStatus(taskId: string): Promise<BacktestStatus> {
    try {
      const response = await apiClient.get(`${this.baseUrl}/status/${taskId}`)
      return response
    } catch (error) {
      console.error('❌ 查询回测状态失败:', error)
      throw new Error('查询回测状态失败')
    }
  }

  /**
   * 获取所有回测任务列表
   */
  async getBacktestTasks(): Promise<BacktestStatus[]> {
    try {
      const response = await apiClient.get(`${this.baseUrl}/tasks`)
      return response
    } catch (error) {
      console.error('❌ 获取回测任务列表失败:', error)
      throw new Error('获取回测任务列表失败')
    }
  }

  /**
   * 删除回测任务
   */
  async deleteBacktestTask(taskId: string): Promise<{ message: string }> {
    try {
      const response = await apiClient.delete(`${this.baseUrl}/tasks/${taskId}`)
      return response
    } catch (error) {
      console.error('❌ 删除回测任务失败:', error)
      throw new Error('删除回测任务失败')
    }
  }

  /**
   * 获取可用策略列表
   */
  async getAvailableStrategies(): Promise<{ strategies: StrategyInfo[] }> {
    try {
      console.log('🔍 正在获取策略列表...', `${this.baseUrl}/strategies`)
      const response = await apiClient.get(`${this.baseUrl}/strategies`)
      console.log('✅ 策略列表API响应:', response)
      return response
    } catch (error) {
      console.error('❌ 获取策略列表失败:', error)
      console.error('错误详情:', error.response?.data || error.message)
      console.error('请求URL:', `${this.baseUrl}/strategies`)
      // 返回默认策略列表
      return {
        strategies: [
          {
            name: 'simple',
            display_name: '简单策略',
            description: '基于移动平均的简单策略',
            parameters: {}
          },
          {
            name: 'moving_average',
            display_name: '移动平均策略',
            description: '双移动平均线交叉策略',
            parameters: {
              fast_period: { type: 'int', default: 10, min: 5, max: 50 },
              slow_period: { type: 'int', default: 20, min: 10, max: 100 }
            }
          },
          {
            name: 'rsi',
            display_name: 'RSI策略',
            description: '基于RSI指标的超买超卖策略',
            parameters: {
              rsi_period: { type: 'int', default: 14, min: 5, max: 30 },
              oversold_threshold: { type: 'float', default: 30, min: 10, max: 40 },
              overbought_threshold: { type: 'float', default: 70, min: 60, max: 90 }
            }
          }
        ]
      }
    }
  }

  /**
   * 获取策略模板
   */
  async getStrategyTemplates(): Promise<{ templates: any; count: number }> {
    try {
      console.log('🔍 获取策略模板')
      const response = await apiClient.get(`${this.baseUrl}/strategy-templates`)
      console.log('✅ 策略模板获取成功:', response)
      return response
    } catch (error) {
      console.error('❌ 获取策略模板失败:', error)
      throw new Error('获取策略模板失败')
    }
  }

  /**
   * 获取技术指标库
   */
  async getIndicatorLibrary(): Promise<{ indicators: any; count: number }> {
    try {
      console.log('🔍 获取技术指标库')
      const response = await apiClient.get(`${this.baseUrl}/indicators`)
      console.log('✅ 技术指标库获取成功:', response)
      return response
    } catch (error) {
      console.error('❌ 获取技术指标库失败:', error)
      throw new Error('获取技术指标库失败')
    }
  }

  /**
   * 轮询回测状态直到完成
   */
  async pollBacktestStatus(
    taskId: string, 
    onProgress?: (status: BacktestStatus) => void,
    maxAttempts: number = 120, // 最多轮询2分钟
    interval: number = 1000 // 每秒轮询一次
  ): Promise<BacktestStatus> {
    let attempts = 0
    
    return new Promise((resolve, reject) => {
      const poll = async () => {
        try {
          attempts++
          const status = await this.getBacktestStatus(taskId)
          
          // 调用进度回调
          if (onProgress) {
            onProgress(status)
          }
          
          // 检查是否完成
          if (status.status === 'completed') {
            resolve(status)
            return
          }
          
          if (status.status === 'failed') {
            reject(new Error(status.message || '回测失败'))
            return
          }
          
          // 检查是否超时
          if (attempts >= maxAttempts) {
            reject(new Error('回测超时'))
            return
          }
          
          // 继续轮询
          setTimeout(poll, interval)
          
        } catch (error) {
          reject(error)
        }
      }
      
      poll()
    })
  }

  /**
   * 一键回测：启动并等待完成
   */
  async runCompleteBacktest(
    request: BacktestRequest,
    onProgress?: (status: BacktestStatus) => void
  ): Promise<BacktestResult> {
    // 启动回测
    const { task_id } = await this.startBacktest(request)

    // 轮询直到完成
    const finalStatus = await this.pollBacktestStatus(task_id, onProgress)

    if (!finalStatus.result) {
      throw new Error('回测完成但未返回结果')
    }

    return finalStatus.result
  }

  /**
   * 获取所有回测任务列表
   */
  async getAllBacktestTasks(): Promise<BacktestStatus[]> {
    try {
      console.log('📋 获取所有回测任务列表')
      const response = await apiClient.get(`${this.baseUrl}/tasks`)
      return response
    } catch (error) {
      console.error('❌ 获取回测任务列表失败:', error)
      throw new Error('获取回测任务列表失败')
    }
  }

  /**
   * 删除回测任务
   */
  async deleteBacktestTask(taskId: string): Promise<void> {
    try {
      console.log('🗑️ 删除回测任务:', taskId)
      await apiClient.delete(`${this.baseUrl}/tasks/${taskId}`)
    } catch (error) {
      console.error('❌ 删除回测任务失败:', error)
      throw new Error('删除回测任务失败')
    }
  }

  /**
   * 验证回测参数
   */
  validateBacktestRequest(request: BacktestRequest): string[] {
    const errors: string[] = []
    
    if (!request.strategy_type) {
      errors.push('请选择策略类型')
    }
    
    if (!request.symbols || request.symbols.length === 0) {
      errors.push('请选择至少一个交易对')
    }
    
    if (!request.start_date) {
      errors.push('请选择开始日期')
    }
    
    if (!request.end_date) {
      errors.push('请选择结束日期')
    }
    
    if (request.start_date && request.end_date) {
      const startDate = new Date(request.start_date)
      const endDate = new Date(request.end_date)
      
      if (startDate >= endDate) {
        errors.push('结束日期必须晚于开始日期')
      }
      
      const daysDiff = (endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24)
      if (daysDiff < 1) {
        errors.push('回测时间范围至少需要1天')
      }
      
      if (daysDiff > 365 * 3) {
        errors.push('回测时间范围不能超过3年')
      }
    }
    
    if (request.initial_capital <= 0) {
      errors.push('初始资金必须大于0')
    }
    
    if (request.initial_capital < 1000) {
      errors.push('初始资金建议不少于1000')
    }
    
    if (request.commission_rate < 0 || request.commission_rate > 0.1) {
      errors.push('手续费率应在0-10%之间')
    }
    
    if (request.slippage_rate < 0 || request.slippage_rate > 0.1) {
      errors.push('滑点率应在0-10%之间')
    }
    
    return errors
  }

  /**
   * 格式化回测结果用于显示
   */
  formatBacktestResult(result: any) {
    console.log('🔍 格式化回测结果:', result)

    const portfolio = result.portfolio || {}
    const trading = result.trading || {}
    const performance = result.performance || {}
    const summary = result.summary || {}

    // 安全地获取数值，提供默认值
    const safeGet = (obj: any, key: string, defaultValue: number = 0) => {
      const value = obj[key]
      return (typeof value === 'number' && !isNaN(value)) ? value : defaultValue
    }

    return {
      // 基本信息
      duration: this.formatDuration(safeGet(summary, 'execution_time')),
      dataPoints: (summary.processed_events || 0).toLocaleString(),

      // 收益指标
      totalReturn: `${safeGet(portfolio, 'total_return').toFixed(2)}%`,
      annualizedReturn: `${safeGet(performance, 'annualized_return').toFixed(2)}%`,
      finalValue: `$${safeGet(portfolio, 'current_value').toLocaleString()}`,
      totalPnL: `$${safeGet(portfolio, 'total_pnl').toLocaleString()}`,

      // 风险指标
      maxDrawdown: `${safeGet(performance, 'max_drawdown').toFixed(2)}%`,
      volatility: `${safeGet(performance, 'volatility').toFixed(2)}%`,
      sharpeRatio: safeGet(performance, 'sharpe_ratio').toFixed(3),

      // 交易指标
      totalTrades: (trading.total_orders || 0).toLocaleString(),
      fillRate: `${(safeGet(trading, 'fill_rate') * 100).toFixed(1)}%`,
      winRate: `${safeGet(performance, 'win_rate').toFixed(1)}%`,
      profitLossRatio: safeGet(performance, 'profit_loss_ratio').toFixed(2),

      // 成本
      totalCommission: `$${safeGet(portfolio, 'total_commission').toLocaleString()}`
    }
  }

  private formatDuration(seconds: number): string {
    if (seconds < 60) {
      return `${seconds.toFixed(1)}秒`
    } else if (seconds < 3600) {
      return `${(seconds / 60).toFixed(1)}分钟`
    } else {
      return `${(seconds / 3600).toFixed(1)}小时`
    }
  }
}

// 导出单例实例
export const backtestService = BacktestService.getInstance()
