import React, { useState } from 'react'
import { motion } from 'framer-motion'
import { Bar<PERSON>hart3, Play, History, Settings, Zap, Target, TrendingUp } from 'lucide-react'
import toast from 'react-hot-toast'
import SimpleBacktestForm from '../components/backtest/SimpleBacktestForm'
import ParametricBacktestForm from '../components/backtest/ParametricBacktestForm'
import ParameterOptimizationForm from '../components/backtest/ParameterOptimizationForm'
import BacktestProgress from '../components/backtest/BacktestProgress'
import BacktestResults from '../components/backtest/BacktestResults'
import EnhancedBacktestResults from '../components/backtest/EnhancedBacktestResults'
import BacktestChart from '../components/backtest/BacktestChart'
import BacktestHistory from '../components/backtest/BacktestHistory'
import { backtestService } from '../services/backtestService'
import type { BacktestRequest, BacktestStatus, BacktestResult } from '../types'

type TabType = 'simple' | 'parametric' | 'optimization' | 'history'

const AnalysisPage: React.FC = () => {
  const [activeTab, setActiveTab] = useState<TabType>('parametric')
  const [backtestStatus, setBacktestStatus] = useState<BacktestStatus | null>(null)
  const [backtestResult, setBacktestResult] = useState<BacktestResult | null>(null)
  const [optimizationResult, setOptimizationResult] = useState<any>(null)
  const [isRunning, setIsRunning] = useState(false)
  const [isOptimizing, setIsOptimizing] = useState(false)

  const handleBacktestSubmit = async (request: BacktestRequest) => {
    console.log('🚀 AnalysisPage handleBacktestSubmit 被调用')
    console.log('📊 回测请求:', request)

    try {
      setIsRunning(true)
      setBacktestResult(null)

      toast.loading('启动回测任务...', { id: 'backtest' })
      console.log('📢 Toast 消息已显示')

      // 启动回测并轮询状态
      console.log('🔄 开始调用 backtestService.runCompleteBacktest')
      const result = await backtestService.runCompleteBacktest(
        request,
        (status) => {
          console.log('📈 回测状态更新:', status)
          setBacktestStatus(status)

          // 更新toast消息
          if (status.status === 'running') {
            toast.loading(`回测进行中... ${Math.round(status.progress * 100)}%`, { id: 'backtest' })
          }
        }
      )

      console.log('✅ 回测完成，结果:', result)
      setBacktestResult(result)
      toast.success('回测完成！', { id: 'backtest' })

    } catch (error) {
      console.error('❌ 回测失败:', error)
      toast.error(error instanceof Error ? error.message : '回测失败', { id: 'backtest' })
    } finally {
      setIsRunning(false)
      console.log('🏁 回测流程结束')
    }
  }

  const handleOptimizationSubmit = async (request: any) => {
    console.log('🔧 参数优化请求:', request)

    try {
      setIsOptimizing(true)
      setOptimizationResult(null)

      toast.loading('启动参数优化...', { id: 'optimization' })

      // 启动参数优化
      const response = await fetch('/api/v1/backtest/optimize-parameters', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(request),
      })

      if (!response.ok) {
        throw new Error(`优化请求失败: ${response.statusText}`)
      }

      const { task_id } = await response.json()
      console.log('🆔 优化任务ID:', task_id)

      // 轮询优化状态
      const pollOptimizationStatus = async () => {
        try {
          const statusResponse = await fetch(`/api/v1/backtest/status/${task_id}`)
          if (!statusResponse.ok) {
            throw new Error('获取优化状态失败')
          }

          const status = await statusResponse.json()
          console.log('📊 优化状态:', status)

          if (status.status === 'running') {
            toast.loading(`参数优化中... ${Math.round(status.progress * 100)}%`, { id: 'optimization' })
            setTimeout(pollOptimizationStatus, 2000)
          } else if (status.status === 'completed') {
            setOptimizationResult(status.result)
            toast.success('参数优化完成！', { id: 'optimization' })
            setIsOptimizing(false)
          } else if (status.status === 'failed') {
            throw new Error(status.message || '优化失败')
          } else {
            setTimeout(pollOptimizationStatus, 1000)
          }
        } catch (error) {
          console.error('轮询优化状态失败:', error)
          toast.error('获取优化状态失败', { id: 'optimization' })
          setIsOptimizing(false)
        }
      }

      setTimeout(pollOptimizationStatus, 1000)

    } catch (error) {
      console.error('❌ 参数优化失败:', error)
      toast.error(`参数优化失败: ${error instanceof Error ? error.message : '未知错误'}`, { id: 'optimization' })
      setIsOptimizing(false)
    }
  }

  const tabs = [
    {
      id: 'simple' as TabType,
      name: '简单回测',
      icon: Play,
      description: '快速策略回测'
    },
    {
      id: 'parametric' as TabType,
      name: '参数化回测',
      icon: Settings,
      description: '自定义策略参数回测'
    },
    {
      id: 'optimization' as TabType,
      name: '参数优化',
      icon: Zap,
      description: '自动寻找最佳参数'
    },
    {
      id: 'history' as TabType,
      name: '历史记录',
      icon: History,
      description: '查看历史回测结果'
    }
  ]

  return (
    <div className="space-y-8">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6 }}
      >
        <div className="flex items-center space-x-3 mb-6">
          <BarChart3 size={32} className="text-primary-400" />
          <div>
            <h1 className="text-3xl font-bold text-white">分析工具</h1>
            <p className="text-dark-400">策略回测、技术分析和自定义工具</p>
          </div>
        </div>

        {/* 标签页导航 */}
        <div className="card p-1 mb-6">
          <div className="flex space-x-1">
            {tabs.map((tab) => {
              const Icon = tab.icon
              return (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`flex-1 flex items-center justify-center space-x-2 px-4 py-3 rounded-lg font-medium transition-all ${
                    activeTab === tab.id
                      ? 'bg-primary-500 text-white shadow-lg'
                      : 'text-dark-300 hover:text-white hover:bg-dark-700'
                  }`}
                >
                  <Icon size={18} />
                  <span>{tab.name}</span>
                </button>
              )
            })}
          </div>
        </div>

        {/* 标签页内容 */}
        <motion.div
          key={activeTab}
          initial={{ opacity: 0, x: 20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.3 }}
        >
          {activeTab === 'simple' && (
            <div className="space-y-6">
              {/* 简化回测表单 */}
              {!isRunning && !backtestResult && (
                <SimpleBacktestForm
                  onSubmit={handleBacktestSubmit}
                  loading={isRunning}
                  disabled={isRunning}
                />
              )}

              {/* 回测进度 */}
              {isRunning && backtestStatus && (
                <BacktestProgress
                  status={backtestStatus}
                  onCancel={() => {
                    setIsRunning(false)
                    setBacktestStatus(null)
                    toast.error('回测已取消', { id: 'backtest' })
                  }}
                />
              )}

              {/* 回测结果 */}
              {backtestResult && (
                <div className="space-y-6">
                  <div className="flex items-center justify-between">
                    <h2 className="text-xl font-bold text-white flex items-center space-x-2">
                      <History size={24} className="text-primary-400" />
                      <span>回测结果</span>
                    </h2>
                    <button
                      onClick={() => {
                        setBacktestResult(null)
                        setBacktestStatus(null)
                      }}
                      className="px-4 py-2 bg-primary-500 hover:bg-primary-600 text-white rounded-lg font-medium transition-colors"
                    >
                      新建回测
                    </button>
                  </div>

                  <BacktestResults result={backtestResult} />
                  <BacktestChart result={backtestResult} />
                </div>
              )}
            </div>
          )}

          {activeTab === 'parametric' && (
            <div className="space-y-6">
              {/* 参数化回测表单 */}
              {!isRunning && !backtestResult && (
                <ParametricBacktestForm
                  onSubmit={handleBacktestSubmit}
                  loading={isRunning}
                  disabled={isRunning}
                />
              )}

              {/* 回测进度 */}
              {isRunning && backtestStatus && (
                <BacktestProgress
                  status={backtestStatus}
                  onCancel={() => {
                    setIsRunning(false)
                    setBacktestStatus(null)
                    toast.error('回测已取消', { id: 'backtest' })
                  }}
                />
              )}

              {/* 增强回测结果 */}
              {backtestResult && (
                <div className="space-y-6">
                  <EnhancedBacktestResults result={backtestResult} />

                  {/* 重新开始按钮 */}
                  <div className="flex justify-center">
                    <button
                      onClick={() => {
                        setBacktestResult(null)
                        setBacktestStatus(null)
                      }}
                      className="flex items-center space-x-2 bg-primary-500 hover:bg-primary-600 text-white font-medium py-2 px-4 rounded-lg transition-colors"
                    >
                      <Settings size={16} />
                      <span>重新配置</span>
                    </button>
                  </div>
                </div>
              )}
            </div>
          )}

          {activeTab === 'optimization' && (
            <div className="space-y-6">
              {/* 参数优化表单 */}
              {!isOptimizing && !optimizationResult && (
                <ParameterOptimizationForm
                  onSubmit={handleOptimizationSubmit}
                  loading={isOptimizing}
                  disabled={isOptimizing}
                />
              )}

              {/* 优化进度 */}
              {isOptimizing && (
                <div className="bg-dark-800 rounded-lg border border-dark-600 p-8">
                  <div className="flex items-center justify-center space-x-3">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-yellow-400"></div>
                    <span className="text-dark-300">正在优化参数...</span>
                  </div>
                </div>
              )}

              {/* 优化结果 */}
              {optimizationResult && (
                <div className="space-y-6">
                  <div className="bg-dark-800 rounded-lg border border-dark-600 p-6">
                    <div className="flex items-center space-x-3 mb-6">
                      <Target className="text-yellow-400" size={24} />
                      <h3 className="text-xl font-bold text-white">优化结果</h3>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div>
                        <h4 className="font-medium text-white mb-3">最佳参数</h4>
                        <div className="space-y-2">
                          {Object.entries(optimizationResult.best_parameters || {}).map(([key, value]) => (
                            <div key={key} className="flex justify-between">
                              <span className="text-dark-300">{key}:</span>
                              <span className="text-white font-medium">{value}</span>
                            </div>
                          ))}
                        </div>
                      </div>

                      <div>
                        <h4 className="font-medium text-white mb-3">优化统计</h4>
                        <div className="space-y-2">
                          <div className="flex justify-between">
                            <span className="text-dark-300">最佳得分:</span>
                            <span className="text-green-400 font-medium">{optimizationResult.best_score?.toFixed(4)}</span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-dark-300">测试组合:</span>
                            <span className="text-white font-medium">{optimizationResult.iterations_completed}</span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-dark-300">优化时间:</span>
                            <span className="text-white font-medium">{optimizationResult.optimization_time?.toFixed(2)}s</span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* 重新开始按钮 */}
                  <div className="flex justify-center">
                    <button
                      onClick={() => {
                        setOptimizationResult(null)
                      }}
                      className="flex items-center space-x-2 bg-yellow-500 hover:bg-yellow-600 text-white font-medium py-2 px-4 rounded-lg transition-colors"
                    >
                      <Zap size={16} />
                      <span>重新优化</span>
                    </button>
                  </div>
                </div>
              )}
            </div>
          )}

          {activeTab === 'history' && (
            <BacktestHistory />
          )}
        </motion.div>
      </motion.div>
    </div>
  )
}

export default AnalysisPage
