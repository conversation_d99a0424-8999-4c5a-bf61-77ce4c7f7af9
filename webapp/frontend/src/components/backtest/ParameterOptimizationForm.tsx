import React, { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import { Zap, Target, Settings, TrendingUp, BarChart3, Clock, Cpu } from 'lucide-react'
import { backtestService } from '../../services/backtestService'

interface ParameterOptimizationFormProps {
  onSubmit: (request: any) => void
  loading?: boolean
  disabled?: boolean
}

interface ParameterRange {
  min: number
  max: number
  step?: number
  type: string
}

const ParameterOptimizationForm: React.FC<ParameterOptimizationFormProps> = ({ 
  onSubmit, 
  loading = false, 
  disabled = false 
}) => {
  const [formData, setFormData] = useState({
    strategy_template: 'dual_moving_average',
    symbols: ['BTCUSDT'],
    start_date: '2024-01-01',
    end_date: '2024-03-31',
    optimization_method: 'grid_search',
    objective_function: 'sharpe_ratio',
    max_iterations: 50,
    parameter_ranges: {} as Record<string, ParameterRange>
  })

  const [templates, setTemplates] = useState<any[]>([])
  const [errors, setErrors] = useState<string[]>([])

  // 可用交易对
  const availableSymbols = [
    'BTCUSDT', 'ETHUSDT', 'BNBUSDT', 'ADAUSDT', 'XRPUSDT',
    'SOLUSDT', 'DOGEUSDT', 'AVAXUSDT', 'TRXUSDT', 'TONUSDT'
  ]

  // 优化方法
  const optimizationMethods = [
    { value: 'grid_search', name: '网格搜索', description: '系统性搜索所有参数组合' },
    { value: 'random_search', name: '随机搜索', description: '随机采样参数空间' },
    { value: 'genetic_algorithm', name: '遗传算法', description: '进化算法优化参数' }
  ]

  // 目标函数
  const objectiveFunctions = [
    { value: 'sharpe_ratio', name: '夏普比率', description: '风险调整后收益' },
    { value: 'total_return', name: '总收益率', description: '绝对收益最大化' },
    { value: 'max_drawdown', name: '最大回撤', description: '最小化最大回撤' },
    { value: 'profit_factor', name: '盈利因子', description: '盈利/亏损比率' }
  ]

  // 策略模板参数配置
  const templateParameters: Record<string, Record<string, any>> = {
    dual_moving_average: {
      fast_period: { min: 5, max: 30, step: 1, type: 'int', description: '快线周期' },
      slow_period: { min: 15, max: 100, step: 5, type: 'int', description: '慢线周期' },
      stop_loss_pct: { min: 1.0, max: 10.0, step: 0.5, type: 'float', description: '止损百分比' },
      take_profit_pct: { min: 2.0, max: 20.0, step: 1.0, type: 'float', description: '止盈百分比' }
    },
    rsi_oversold: {
      rsi_period: { min: 5, max: 30, step: 1, type: 'int', description: 'RSI周期' },
      oversold_threshold: { min: 15, max: 40, step: 1, type: 'float', description: '超卖阈值' },
      overbought_threshold: { min: 60, max: 85, step: 1, type: 'float', description: '超买阈值' },
      stop_loss_pct: { min: 1.0, max: 8.0, step: 0.5, type: 'float', description: '止损百分比' }
    },
    multi_indicator: {
      sma_period: { min: 10, max: 50, step: 2, type: 'int', description: 'SMA周期' },
      rsi_period: { min: 5, max: 25, step: 1, type: 'int', description: 'RSI周期' },
      macd_fast: { min: 8, max: 16, step: 1, type: 'int', description: 'MACD快线' },
      macd_slow: { min: 20, max: 35, step: 1, type: 'int', description: 'MACD慢线' }
    }
  }

  useEffect(() => {
    loadTemplates()
    initializeParameterRanges()
  }, [])

  useEffect(() => {
    initializeParameterRanges()
  }, [formData.strategy_template])

  const loadTemplates = async () => {
    try {
      const result = await backtestService.getStrategyTemplates()
      if (result && result.templates) {
        setTemplates(Object.keys(result.templates).map(key => ({
          name: key,
          display_name: key.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase()),
          description: `${key.replace(/_/g, ' ')} 策略模板`
        })))
      }
    } catch (error) {
      console.error('加载模板失败:', error)
      setTemplates([
        { name: 'dual_moving_average', display_name: '双移动平均', description: '双移动平均线交叉策略' },
        { name: 'rsi_oversold', display_name: 'RSI超卖', description: 'RSI超买超卖策略' },
        { name: 'multi_indicator', display_name: '多指标', description: '多技术指标综合策略' }
      ])
    }
  }

  const initializeParameterRanges = () => {
    const templateParams = templateParameters[formData.strategy_template]
    if (templateParams) {
      const ranges: Record<string, ParameterRange> = {}
      Object.entries(templateParams).forEach(([key, config]: [string, any]) => {
        ranges[key] = {
          min: config.min,
          max: config.max,
          step: config.step,
          type: config.type
        }
      })
      setFormData(prev => ({ ...prev, parameter_ranges: ranges }))
    }
  }

  const handleInputChange = (field: string, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }))
  }

  const handleParameterRangeChange = (paramName: string, field: 'min' | 'max' | 'step', value: number) => {
    setFormData(prev => ({
      ...prev,
      parameter_ranges: {
        ...prev.parameter_ranges,
        [paramName]: {
          ...prev.parameter_ranges[paramName],
          [field]: value
        }
      }
    }))
  }

  const handleSymbolToggle = (symbol: string) => {
    setFormData(prev => ({
      ...prev,
      symbols: prev.symbols.includes(symbol)
        ? prev.symbols.filter(s => s !== symbol)
        : [...prev.symbols, symbol]
    }))
  }

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    console.log('🚀 ParameterOptimizationForm 表单提交')
    console.log('📊 表单数据:', formData)

    // 验证
    const validationErrors = []
    if (!formData.strategy_template) validationErrors.push('请选择策略模板')
    if (!formData.symbols || formData.symbols.length === 0) validationErrors.push('请选择交易对')
    if (!formData.start_date) validationErrors.push('请选择开始日期')
    if (!formData.end_date) validationErrors.push('请选择结束日期')
    if (Object.keys(formData.parameter_ranges).length === 0) validationErrors.push('请配置参数范围')

    setErrors(validationErrors)
    console.log('🔍 验证结果:', validationErrors)

    if (validationErrors.length === 0) {
      console.log('✅ 验证通过，调用 onSubmit')
      onSubmit(formData)
    } else {
      console.log('❌ 验证失败:', validationErrors)
    }
  }

  const currentTemplateParams = templateParameters[formData.strategy_template] || {}

  return (
    <div className="bg-dark-800 rounded-lg border border-dark-600 overflow-hidden">
      {/* 标题 */}
      <div className="bg-gradient-to-r from-yellow-500 to-orange-500 p-6">
        <div className="flex items-center space-x-3">
          <Zap className="text-white" size={28} />
          <div>
            <h2 className="text-2xl font-bold text-white">参数自动优化</h2>
            <p className="text-yellow-100">自动搜索最佳策略参数组合</p>
          </div>
        </div>
      </div>

      <form onSubmit={handleSubmit} className="p-6 space-y-8">
        {/* 策略模板选择 */}
        <div className="space-y-4">
          <h3 className="text-lg font-semibold text-white flex items-center space-x-2">
            <Target size={20} className="text-yellow-400" />
            <span>策略模板</span>
          </h3>
          
          <div className="grid gap-3">
            {templates.map(template => (
              <div
                key={template.name}
                className={`p-4 rounded-lg border cursor-pointer transition-all ${
                  formData.strategy_template === template.name
                    ? 'border-yellow-400 bg-yellow-500/10'
                    : 'border-dark-600 hover:border-dark-500'
                }`}
                onClick={() => handleInputChange('strategy_template', template.name)}
              >
                <div className="flex items-center space-x-3">
                  <input
                    type="radio"
                    checked={formData.strategy_template === template.name}
                    onChange={() => handleInputChange('strategy_template', template.name)}
                    className="text-yellow-400"
                  />
                  <div>
                    <h4 className="font-medium text-white">{template.display_name}</h4>
                    <p className="text-sm text-dark-400">{template.description}</p>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* 参数范围配置 */}
        <div className="space-y-4">
          <h3 className="text-lg font-semibold text-white flex items-center space-x-2">
            <Settings size={20} className="text-yellow-400" />
            <span>参数搜索范围</span>
          </h3>
          
          <div className="grid gap-4">
            {Object.entries(currentTemplateParams).map(([paramName, config]: [string, any]) => (
              <div key={paramName} className="bg-dark-700 p-4 rounded-lg">
                <h4 className="font-medium text-white mb-3">{config.description}</h4>
                <div className="grid grid-cols-3 gap-4">
                  <div>
                    <label className="block text-xs text-dark-400 mb-1">最小值</label>
                    <input
                      type="number"
                      value={formData.parameter_ranges[paramName]?.min || config.min}
                      onChange={(e) => handleParameterRangeChange(paramName, 'min', parseFloat(e.target.value))}
                      step={config.step}
                      className="w-full px-2 py-1 bg-dark-600 border border-dark-500 rounded text-white text-sm"
                      disabled={disabled}
                    />
                  </div>
                  <div>
                    <label className="block text-xs text-dark-400 mb-1">最大值</label>
                    <input
                      type="number"
                      value={formData.parameter_ranges[paramName]?.max || config.max}
                      onChange={(e) => handleParameterRangeChange(paramName, 'max', parseFloat(e.target.value))}
                      step={config.step}
                      className="w-full px-2 py-1 bg-dark-600 border border-dark-500 rounded text-white text-sm"
                      disabled={disabled}
                    />
                  </div>
                  <div>
                    <label className="block text-xs text-dark-400 mb-1">步长</label>
                    <input
                      type="number"
                      value={formData.parameter_ranges[paramName]?.step || config.step}
                      onChange={(e) => handleParameterRangeChange(paramName, 'step', parseFloat(e.target.value))}
                      step={config.type === 'int' ? 1 : 0.1}
                      className="w-full px-2 py-1 bg-dark-600 border border-dark-500 rounded text-white text-sm"
                      disabled={disabled}
                    />
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* 优化设置 */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* 优化方法 */}
          <div>
            <h3 className="text-lg font-semibold text-white flex items-center space-x-2 mb-4">
              <Cpu size={20} className="text-yellow-400" />
              <span>优化方法</span>
            </h3>
            <div className="space-y-2">
              {optimizationMethods.map(method => (
                <label key={method.value} className="flex items-start space-x-3 cursor-pointer p-3 rounded border border-dark-600 hover:border-dark-500">
                  <input
                    type="radio"
                    name="optimization_method"
                    value={method.value}
                    checked={formData.optimization_method === method.value}
                    onChange={(e) => handleInputChange('optimization_method', e.target.value)}
                    className="mt-1 text-yellow-400"
                    disabled={disabled}
                  />
                  <div>
                    <div className="font-medium text-white">{method.name}</div>
                    <div className="text-sm text-dark-400">{method.description}</div>
                  </div>
                </label>
              ))}
            </div>
          </div>

          {/* 目标函数 */}
          <div>
            <h3 className="text-lg font-semibold text-white flex items-center space-x-2 mb-4">
              <BarChart3 size={20} className="text-yellow-400" />
              <span>优化目标</span>
            </h3>
            <div className="space-y-2">
              {objectiveFunctions.map(func => (
                <label key={func.value} className="flex items-start space-x-3 cursor-pointer p-3 rounded border border-dark-600 hover:border-dark-500">
                  <input
                    type="radio"
                    name="objective_function"
                    value={func.value}
                    checked={formData.objective_function === func.value}
                    onChange={(e) => handleInputChange('objective_function', e.target.value)}
                    className="mt-1 text-yellow-400"
                    disabled={disabled}
                  />
                  <div>
                    <div className="font-medium text-white">{func.name}</div>
                    <div className="text-sm text-dark-400">{func.description}</div>
                  </div>
                </label>
              ))}
            </div>
          </div>
        </div>

        {/* 基础设置 */}
        <div className="space-y-4">
          <h3 className="text-lg font-semibold text-white">基础设置</h3>
          
          {/* 交易对选择 */}
          <div>
            <label className="block text-sm font-medium text-dark-300 mb-3">交易对选择</label>
            <div className="grid grid-cols-2 md:grid-cols-5 gap-2">
              {availableSymbols.map(symbol => (
                <label key={symbol} className="flex items-center space-x-2 cursor-pointer p-2 rounded border border-dark-600 hover:border-dark-500">
                  <input
                    type="checkbox"
                    checked={formData.symbols.includes(symbol)}
                    onChange={() => handleSymbolToggle(symbol)}
                    className="rounded border-dark-600 bg-dark-700 text-yellow-400 focus:ring-yellow-400"
                    disabled={disabled}
                  />
                  <span className="text-sm text-dark-300">{symbol}</span>
                </label>
              ))}
            </div>
          </div>

          {/* 时间范围和迭代次数 */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <label className="block text-sm font-medium text-dark-300 mb-2">开始日期</label>
              <input
                type="date"
                value={formData.start_date}
                onChange={(e) => handleInputChange('start_date', e.target.value)}
                className="w-full px-3 py-2 bg-dark-700 border border-dark-600 rounded-lg text-white focus:border-yellow-400 focus:outline-none"
                disabled={disabled}
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-dark-300 mb-2">结束日期</label>
              <input
                type="date"
                value={formData.end_date}
                onChange={(e) => handleInputChange('end_date', e.target.value)}
                className="w-full px-3 py-2 bg-dark-700 border border-dark-600 rounded-lg text-white focus:border-yellow-400 focus:outline-none"
                disabled={disabled}
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-dark-300 mb-2">最大迭代次数</label>
              <input
                type="number"
                value={formData.max_iterations}
                onChange={(e) => handleInputChange('max_iterations', parseInt(e.target.value))}
                min="10"
                max="500"
                step="10"
                className="w-full px-3 py-2 bg-dark-700 border border-dark-600 rounded-lg text-white focus:border-yellow-400 focus:outline-none"
                disabled={disabled}
              />
            </div>
          </div>
        </div>

        {/* 错误提示 */}
        {errors.length > 0 && (
          <div className="bg-red-900/20 border border-red-500/30 rounded-lg p-4">
            <h4 className="text-red-400 font-medium mb-2">请修正以下错误：</h4>
            <ul className="text-red-300 text-sm space-y-1">
              {errors.map((error, index) => (
                <li key={index}>• {error}</li>
              ))}
            </ul>
          </div>
        )}

        {/* 提交按钮 */}
        <button
          type="submit"
          disabled={disabled || loading}
          onClick={(e) => {
            console.log('🎯 参数优化提交按钮被点击')
            // 不阻止默认行为，让表单正常提交
          }}
          className="w-full flex items-center justify-center space-x-2 bg-gradient-to-r from-yellow-500 to-orange-500 hover:from-yellow-600 hover:to-orange-600 disabled:from-dark-600 disabled:to-dark-600 disabled:cursor-not-allowed text-white font-medium py-3 px-4 rounded-lg transition-all"
        >
          <Zap size={20} />
          <span>{loading ? '优化中...' : '开始参数优化'}</span>
        </button>
      </form>
    </div>
  )
}

export default ParameterOptimizationForm
