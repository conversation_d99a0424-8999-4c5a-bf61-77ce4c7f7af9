import React, { useState, useEffect } from 'react'
import { Play } from 'lucide-react'
import { backtestService } from '../../services/backtestService'
import type { BacktestRequest, StrategyInfo } from '../../types'

interface SimpleBacktestFormProps {
  onSubmit: (request: BacktestRequest) => void
  loading?: boolean
  disabled?: boolean
}

const SimpleBacktestForm: React.FC<SimpleBacktestFormProps> = ({ 
  onSubmit, 
  loading = false, 
  disabled = false 
}) => {
  const [formData, setFormData] = useState<BacktestRequest>({
    strategy_type: 'moving_average',
    symbols: ['BTCUSDT'],
    start_date: '2024-01-01',
    end_date: '2024-06-30',
    initial_capital: 100000,
    commission_rate: 0.001,
    slippage_rate: 0.0005,
    strategy_params: {}
  })

  const [strategies, setStrategies] = useState<StrategyInfo[]>([])
  const [errors, setErrors] = useState<string[]>([])

  // 可用交易对
  const availableSymbols = [
    'BTCUSDT', 'ETHUSDT', 'BNBUSDT', 'ADAUSDT', 'XRPUSDT'
  ]

  useEffect(() => {
    console.log('🔄 SimpleBacktestForm 组件挂载')
    loadStrategies()
  }, [])

  const loadStrategies = async () => {
    try {
      console.log('🔍 开始加载策略列表...')
      const result = await backtestService.getAvailableStrategies()
      console.log('✅ 策略列表加载成功:', result)
      
      if (result && result.strategies) {
        setStrategies(result.strategies)
      } else {
        // 使用默认策略
        setStrategies([
          {
            name: 'moving_average',
            display_name: '移动平均策略',
            description: '基于移动平均线的交易策略',
            parameters: {}
          }
        ])
      }
    } catch (error) {
      console.error('❌ 加载策略失败:', error)
      setStrategies([
        {
          name: 'moving_average',
          display_name: '移动平均策略',
          description: '基于移动平均线的交易策略',
          parameters: {}
        }
      ])
    }
  }

  const handleInputChange = (field: keyof BacktestRequest, value: any) => {
    console.log(`📝 输入变更: ${field} = ${value}`)
    setFormData(prev => ({ ...prev, [field]: value }))
  }

  const handleSymbolToggle = (symbol: string) => {
    setFormData(prev => ({
      ...prev,
      symbols: prev.symbols.includes(symbol)
        ? prev.symbols.filter(s => s !== symbol)
        : [...prev.symbols, symbol]
    }))
  }

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    console.log('🚀 SimpleBacktestForm 表单提交')
    console.log('📊 表单数据:', formData)
    
    // 简单验证
    const validationErrors = []
    if (!formData.strategy_type) validationErrors.push('请选择策略类型')
    if (!formData.symbols || formData.symbols.length === 0) validationErrors.push('请选择交易对')
    if (!formData.start_date) validationErrors.push('请选择开始日期')
    if (!formData.end_date) validationErrors.push('请选择结束日期')
    
    setErrors(validationErrors)
    console.log('🔍 验证结果:', validationErrors)
    
    if (validationErrors.length === 0) {
      console.log('✅ 验证通过，调用 onSubmit')
      onSubmit(formData)
    } else {
      console.log('❌ 验证失败:', validationErrors)
    }
  }

  const selectedStrategy = strategies.find(s => s.name === formData.strategy_type)

  console.log('🔄 SimpleBacktestForm 渲染', {
    disabled,
    loading,
    strategiesCount: strategies.length,
    errorsCount: errors.length
  })

  return (
    <div className="bg-dark-800 p-6 rounded-lg border border-dark-600">
      <div className="flex items-center space-x-3 mb-6">
        <Play className="text-primary-400" size={24} />
        <h2 className="text-xl font-bold text-white">简化回测配置</h2>
      </div>

      <form onSubmit={handleSubmit} className="space-y-6">
        {/* 策略选择 */}
        <div>
          <label className="block text-sm font-medium text-dark-300 mb-2">
            交易策略
          </label>
          <select
            value={formData.strategy_type}
            onChange={(e) => handleInputChange('strategy_type', e.target.value)}
            className="w-full px-3 py-2 bg-dark-700 border border-dark-600 rounded-lg text-white focus:border-primary-400 focus:outline-none"
            disabled={disabled}
          >
            {strategies.map(strategy => (
              <option key={strategy.name} value={strategy.name}>
                {strategy.display_name}
              </option>
            ))}
          </select>
          {selectedStrategy && (
            <p className="text-sm text-dark-400 mt-1">{selectedStrategy.description}</p>
          )}
        </div>

        {/* 交易对选择 */}
        <div>
          <label className="block text-sm font-medium text-dark-300 mb-2">交易对</label>
          <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
            {availableSymbols.map(symbol => (
              <label key={symbol} className="flex items-center space-x-2 cursor-pointer">
                <input
                  type="checkbox"
                  checked={formData.symbols.includes(symbol)}
                  onChange={() => handleSymbolToggle(symbol)}
                  className="rounded border-dark-600 bg-dark-700 text-primary-400 focus:ring-primary-400"
                  disabled={disabled}
                />
                <span className="text-sm text-dark-300">{symbol}</span>
              </label>
            ))}
          </div>
        </div>

        {/* 时间范围 */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-dark-300 mb-2">
              开始日期
            </label>
            <input
              type="date"
              value={formData.start_date}
              onChange={(e) => handleInputChange('start_date', e.target.value)}
              className="w-full px-3 py-2 bg-dark-700 border border-dark-600 rounded-lg text-white focus:border-primary-400 focus:outline-none"
              disabled={disabled}
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-dark-300 mb-2">结束日期</label>
            <input
              type="date"
              value={formData.end_date}
              onChange={(e) => handleInputChange('end_date', e.target.value)}
              className="w-full px-3 py-2 bg-dark-700 border border-dark-600 rounded-lg text-white focus:border-primary-400 focus:outline-none"
              disabled={disabled}
            />
          </div>
        </div>

        {/* 初始资金 */}
        <div>
          <label className="block text-sm font-medium text-dark-300 mb-2">
            初始资金 (USD)
          </label>
          <input
            type="number"
            value={formData.initial_capital}
            onChange={(e) => handleInputChange('initial_capital', parseFloat(e.target.value))}
            min="1000"
            step="1000"
            className="w-full px-3 py-2 bg-dark-700 border border-dark-600 rounded-lg text-white focus:border-primary-400 focus:outline-none"
            disabled={disabled}
          />
        </div>

        {/* 错误提示 */}
        {errors.length > 0 && (
          <div className="bg-red-900/20 border border-red-500/30 rounded-lg p-4">
            <h4 className="text-red-400 font-medium mb-2">请修正以下错误：</h4>
            <ul className="text-red-300 text-sm space-y-1">
              {errors.map((error, index) => (
                <li key={index}>• {error}</li>
              ))}
            </ul>
          </div>
        )}

        {/* 提交按钮 */}
        <button
          type="submit"
          disabled={disabled || loading}
          onClick={(e) => {
            console.log('🎯 提交按钮被点击')
            // 不阻止默认行为，让表单正常提交
          }}
          className="w-full flex items-center justify-center space-x-2 bg-primary-500 hover:bg-primary-600 disabled:bg-dark-600 disabled:cursor-not-allowed text-white font-medium py-3 px-4 rounded-lg transition-colors"
        >
          <Play size={20} />
          <span>{loading ? '启动中...' : '开始回测'}</span>
        </button>
      </form>
    </div>
  )
}

export default SimpleBacktestForm
