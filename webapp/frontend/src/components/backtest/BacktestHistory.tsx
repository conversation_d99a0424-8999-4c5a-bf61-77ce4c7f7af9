import React, { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { 
  History, 
  Clock, 
  CheckCircle, 
  XCircle, 
  AlertCircle, 
  Trash2, 
  Eye, 
  RefreshCw,
  Calendar,
  TrendingUp,
  DollarSign,
  Activity
} from 'lucide-react'
import toast from 'react-hot-toast'
import { backtestService } from '../../services/backtestService'
import type { BacktestStatus } from '../../types'
import BacktestResults from './BacktestResults'

interface BacktestHistoryProps {
  className?: string
}

const BacktestHistory: React.FC<BacktestHistoryProps> = ({ className = '' }) => {
  const [tasks, setTasks] = useState<BacktestStatus[]>([])
  const [loading, setLoading] = useState(true)
  const [selectedTask, setSelectedTask] = useState<BacktestStatus | null>(null)
  const [showResults, setShowResults] = useState(false)

  // 加载历史任务
  const loadTasks = async () => {
    try {
      setLoading(true)
      const taskList = await backtestService.getAllBacktestTasks()
      // 按创建时间倒序排列
      const sortedTasks = taskList.sort((a, b) => 
        new Date(b.created_at || '').getTime() - new Date(a.created_at || '').getTime()
      )
      setTasks(sortedTasks)
    } catch (error) {
      console.error('加载历史任务失败:', error)
      toast.error('加载历史任务失败')
    } finally {
      setLoading(false)
    }
  }

  // 删除任务
  const handleDeleteTask = async (taskId: string) => {
    if (!confirm('确定要删除这个回测任务吗？')) {
      return
    }

    try {
      await backtestService.deleteBacktestTask(taskId)
      toast.success('任务已删除')
      // 重新加载任务列表
      await loadTasks()
    } catch (error) {
      console.error('删除任务失败:', error)
      toast.error('删除任务失败')
    }
  }

  // 查看任务结果
  const handleViewResults = (task: BacktestStatus) => {
    if (task.status === 'completed' && task.result) {
      setSelectedTask(task)
      setShowResults(true)
    } else {
      toast.error('该任务尚未完成或无结果数据')
    }
  }

  // 获取状态图标
  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return <CheckCircle size={16} className="text-green-400" />
      case 'failed':
        return <XCircle size={16} className="text-red-400" />
      case 'running':
        return <RefreshCw size={16} className="text-blue-400 animate-spin" />
      case 'pending':
        return <Clock size={16} className="text-yellow-400" />
      default:
        return <AlertCircle size={16} className="text-gray-400" />
    }
  }

  // 获取状态文本
  const getStatusText = (status: string) => {
    switch (status) {
      case 'completed':
        return '已完成'
      case 'failed':
        return '失败'
      case 'running':
        return '运行中'
      case 'pending':
        return '等待中'
      default:
        return '未知'
    }
  }

  // 获取状态颜色
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'text-green-400 bg-green-900/20'
      case 'failed':
        return 'text-red-400 bg-red-900/20'
      case 'running':
        return 'text-blue-400 bg-blue-900/20'
      case 'pending':
        return 'text-yellow-400 bg-yellow-900/20'
      default:
        return 'text-gray-400 bg-gray-900/20'
    }
  }

  // 格式化时间
  const formatTime = (timeStr?: string) => {
    if (!timeStr) return '-'
    const date = new Date(timeStr)
    return date.toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  // 格式化收益率
  const formatReturn = (result: any) => {
    if (!result?.portfolio?.total_return) return '-'
    const returnRate = result.portfolio.total_return
    const isPositive = returnRate > 0
    return (
      <span className={isPositive ? 'text-green-400' : 'text-red-400'}>
        {isPositive ? '+' : ''}{returnRate.toFixed(2)}%
      </span>
    )
  }

  useEffect(() => {
    loadTasks()
  }, [])

  if (showResults && selectedTask) {
    return (
      <div className={`space-y-6 ${className}`}>
        {/* 返回按钮 */}
        <div className="flex items-center space-x-4">
          <button
            onClick={() => {
              setShowResults(false)
              setSelectedTask(null)
            }}
            className="flex items-center space-x-2 text-primary-400 hover:text-primary-300 transition-colors"
          >
            <History size={20} />
            <span>返回历史记录</span>
          </button>
          <div className="text-dark-400">
            任务ID: {selectedTask.task_id}
          </div>
        </div>

        {/* 显示回测结果 */}
        <BacktestResults result={selectedTask.result} />
      </div>
    )
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* 标题和刷新按钮 */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3">
          <History className="text-primary-400" size={24} />
          <h2 className="text-xl font-bold text-white">回测历史记录</h2>
        </div>
        <button
          onClick={loadTasks}
          disabled={loading}
          className="flex items-center space-x-2 bg-primary-500 hover:bg-primary-600 disabled:bg-dark-600 disabled:cursor-not-allowed text-white font-medium py-2 px-4 rounded-lg transition-colors"
        >
          <RefreshCw size={16} className={loading ? 'animate-spin' : ''} />
          <span>刷新</span>
        </button>
      </div>

      {/* 任务列表 */}
      <div className="bg-dark-800 rounded-lg border border-dark-600 overflow-hidden">
        {loading ? (
          <div className="p-8 text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-400 mx-auto mb-4"></div>
            <p className="text-dark-300">加载历史记录中...</p>
          </div>
        ) : tasks.length === 0 ? (
          <div className="p-8 text-center">
            <History className="mx-auto text-dark-500 mb-4" size={48} />
            <h3 className="text-lg font-medium text-white mb-2">暂无历史记录</h3>
            <p className="text-dark-400">还没有运行过回测任务</p>
          </div>
        ) : (
          <div className="divide-y divide-dark-600">
            <AnimatePresence>
              {tasks.map((task, index) => (
                <motion.div
                  key={task.task_id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -20 }}
                  transition={{ delay: index * 0.1 }}
                  className="p-6 hover:bg-dark-700/50 transition-colors"
                >
                  <div className="flex items-center justify-between">
                    {/* 任务信息 */}
                    <div className="flex-1 space-y-2">
                      <div className="flex items-center space-x-3">
                        {getStatusIcon(task.status)}
                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(task.status)}`}>
                          {getStatusText(task.status)}
                        </span>
                        <span className="text-dark-400 text-sm">
                          任务ID: {task.task_id.slice(0, 8)}...
                        </span>
                      </div>
                      
                      <div className="flex items-center space-x-6 text-sm text-dark-300">
                        <div className="flex items-center space-x-1">
                          <Calendar size={14} />
                          <span>创建时间: {formatTime(task.created_at)}</span>
                        </div>
                        
                        {task.status === 'completed' && task.result && (
                          <>
                            <div className="flex items-center space-x-1">
                              <TrendingUp size={14} />
                              <span>收益率: {formatReturn(task.result)}</span>
                            </div>
                            <div className="flex items-center space-x-1">
                              <DollarSign size={14} />
                              <span>最终价值: ${task.result.portfolio?.final_value?.toLocaleString() || '-'}</span>
                            </div>
                            <div className="flex items-center space-x-1">
                              <Activity size={14} />
                              <span>交易次数: {task.result.portfolio?.total_trades || 0}</span>
                            </div>
                          </>
                        )}
                      </div>

                      {task.message && (
                        <p className="text-sm text-dark-400">{task.message}</p>
                      )}
                    </div>

                    {/* 操作按钮 */}
                    <div className="flex items-center space-x-2">
                      {task.status === 'completed' && task.result && (
                        <button
                          onClick={() => handleViewResults(task)}
                          className="flex items-center space-x-1 bg-blue-500 hover:bg-blue-600 text-white text-sm font-medium py-1 px-3 rounded transition-colors"
                        >
                          <Eye size={14} />
                          <span>查看</span>
                        </button>
                      )}
                      
                      <button
                        onClick={() => handleDeleteTask(task.task_id)}
                        className="flex items-center space-x-1 bg-red-500 hover:bg-red-600 text-white text-sm font-medium py-1 px-3 rounded transition-colors"
                      >
                        <Trash2 size={14} />
                        <span>删除</span>
                      </button>
                    </div>
                  </div>
                </motion.div>
              ))}
            </AnimatePresence>
          </div>
        )}
      </div>
    </div>
  )
}

export default BacktestHistory
