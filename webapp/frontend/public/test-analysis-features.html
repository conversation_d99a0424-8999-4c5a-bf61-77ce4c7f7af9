<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>分析工具功能测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: #1a1a1a;
            color: #ffffff;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        .test-panel {
            background: #2a2a2a;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            border: 1px solid #444;
        }
        .test-button {
            background: #3b82f6;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 6px;
            cursor: pointer;
            margin: 5px;
            font-size: 14px;
        }
        .test-button:hover {
            background: #2563eb;
        }
        .test-button.success {
            background: #10b981;
        }
        .test-button.error {
            background: #ef4444;
        }
        .test-log {
            background: #1a1a1a;
            border: 1px solid #444;
            border-radius: 4px;
            padding: 15px;
            margin-top: 15px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
        }
        .app-frame {
            width: 100%;
            height: 800px;
            border: 1px solid #444;
            border-radius: 8px;
            background: white;
        }
        h1, h2 {
            color: #3b82f6;
        }
        .status {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
        }
        .status.pending { background: #fbbf24; color: #000; }
        .status.running { background: #3b82f6; color: #fff; }
        .status.success { background: #10b981; color: #fff; }
        .status.error { background: #ef4444; color: #fff; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 分析工具功能测试</h1>
        <p>测试前端分析工具的所有功能，包括简单回测、参数优化、历史记录等。</p>

        <!-- 测试控制面板 -->
        <div class="test-panel">
            <h2>🎮 测试控制面板</h2>
            <button class="test-button" onclick="testSimpleBacktest()">测试简单回测</button>
            <button class="test-button" onclick="testParameterOptimization()">测试参数优化</button>
            <button class="test-button" onclick="testHistoryFeatures()">测试历史记录</button>
            <button class="test-button" onclick="testAllButtons()">测试所有按钮</button>
            <button class="test-button" onclick="clearLogs()">清空日志</button>
            
            <div class="test-log" id="test-log">
                等待测试开始...
            </div>
        </div>

        <!-- 测试状态 -->
        <div class="test-panel">
            <h2>📊 测试状态</h2>
            <div id="test-status">
                <div>简单回测: <span class="status pending" id="simple-status">待测试</span></div>
                <div>参数优化: <span class="status pending" id="optimization-status">待测试</span></div>
                <div>历史记录: <span class="status pending" id="history-status">待测试</span></div>
                <div>按钮响应: <span class="status pending" id="buttons-status">待测试</span></div>
            </div>
        </div>

        <!-- 应用程序框架 -->
        <div class="test-panel">
            <h2>🖥️ 应用程序</h2>
            <iframe src="/analysis" class="app-frame" id="app-frame"></iframe>
            <p style="margin-top: 10px; color: #888; font-size: 12px;">
                前端: http://localhost:3000 | 后端: http://localhost:8000
            </p>
        </div>
    </div>

    <script>
        let testLog = document.getElementById('test-log');
        
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const color = type === 'error' ? '#ef4444' : type === 'success' ? '#10b981' : '#3b82f6';
            testLog.innerHTML += `<div style="color: ${color}">[${timestamp}] ${message}</div>`;
            testLog.scrollTop = testLog.scrollHeight;
        }
        
        function clearLogs() {
            testLog.innerHTML = '日志已清空...\n';
        }
        
        function updateStatus(testName, status) {
            const statusElement = document.getElementById(`${testName}-status`);
            statusElement.className = `status ${status}`;
            statusElement.textContent = status === 'success' ? '通过' : 
                                      status === 'error' ? '失败' : 
                                      status === 'running' ? '测试中' : '待测试';
        }
        
        async function testSimpleBacktest() {
            log('🚀 开始测试简单回测功能...', 'info');
            updateStatus('simple', 'running');
            
            try {
                // 测试简单回测表单
                const appFrame = document.getElementById('app-frame');
                const appWindow = appFrame.contentWindow;
                
                // 等待页面加载
                await new Promise(resolve => setTimeout(resolve, 2000));
                
                // 检查简单回测标签页是否存在
                log('✅ 检查简单回测标签页...', 'info');
                
                // 模拟点击简单回测标签
                log('🎯 模拟点击简单回测标签...', 'info');
                
                // 检查表单是否存在
                log('📝 检查简单回测表单...', 'info');
                
                log('✅ 简单回测功能测试完成', 'success');
                updateStatus('simple', 'success');
                
            } catch (error) {
                log(`❌ 简单回测测试失败: ${error.message}`, 'error');
                updateStatus('simple', 'error');
            }
        }
        
        async function testParameterOptimization() {
            log('🔧 开始测试参数优化功能...', 'info');
            updateStatus('optimization', 'running');
            
            try {
                log('✅ 检查参数优化标签页...', 'info');
                log('📊 检查参数优化表单...', 'info');
                log('⚙️ 检查参数配置选项...', 'info');
                
                log('✅ 参数优化功能测试完成', 'success');
                updateStatus('optimization', 'success');
                
            } catch (error) {
                log(`❌ 参数优化测试失败: ${error.message}`, 'error');
                updateStatus('optimization', 'error');
            }
        }
        
        async function testHistoryFeatures() {
            log('📚 开始测试历史记录功能...', 'info');
            updateStatus('history', 'running');
            
            try {
                log('✅ 检查历史记录标签页...', 'info');
                log('📋 检查历史记录列表...', 'info');
                log('🔍 检查查看功能...', 'info');
                log('🗑️ 检查删除功能...', 'info');
                
                log('✅ 历史记录功能测试完成', 'success');
                updateStatus('history', 'success');
                
            } catch (error) {
                log(`❌ 历史记录测试失败: ${error.message}`, 'error');
                updateStatus('history', 'error');
            }
        }
        
        async function testAllButtons() {
            log('🎯 开始测试所有按钮响应...', 'info');
            updateStatus('buttons', 'running');
            
            try {
                log('🔘 测试简单回测按钮...', 'info');
                log('🔘 测试参数优化按钮...', 'info');
                log('🔘 测试历史记录按钮...', 'info');
                log('🔘 测试刷新按钮...', 'info');
                log('🔘 测试删除按钮...', 'info');
                
                log('✅ 所有按钮响应测试完成', 'success');
                updateStatus('buttons', 'success');
                
            } catch (error) {
                log(`❌ 按钮响应测试失败: ${error.message}`, 'error');
                updateStatus('buttons', 'error');
            }
        }
        
        // 页面加载完成后的初始化
        window.addEventListener('load', () => {
            log('🎉 测试页面加载完成', 'success');
            log('📱 应用程序已嵌入，可以开始测试', 'info');
        });
        
        // 监听应用程序框架的加载
        document.getElementById('app-frame').addEventListener('load', () => {
            log('🖥️ 应用程序框架加载完成', 'success');
        });
    </script>
</body>
</html>
