<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>端口配置验证</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: #1a1a1a;
            color: #ffffff;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
        }
        .test-panel {
            background: #2a2a2a;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            border: 1px solid #444;
        }
        .status {
            display: inline-block;
            padding: 4px 12px;
            border-radius: 4px;
            font-size: 14px;
            font-weight: bold;
            margin: 5px;
        }
        .status.success { background: #10b981; color: #fff; }
        .status.error { background: #ef4444; color: #fff; }
        .status.testing { background: #3b82f6; color: #fff; }
        .test-button {
            background: #3b82f6;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 6px;
            cursor: pointer;
            margin: 5px;
            font-size: 14px;
        }
        .test-button:hover {
            background: #2563eb;
        }
        .test-log {
            background: #1a1a1a;
            border: 1px solid #444;
            border-radius: 4px;
            padding: 15px;
            margin-top: 15px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
        }
        h1, h2 {
            color: #3b82f6;
        }
        .config-info {
            background: #1e3a8a;
            color: #dbeafe;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 端口配置验证</h1>
        
        <div class="config-info">
            <h3>📋 当前配置</h3>
            <p><strong>前端端口:</strong> 3000 (固定)</p>
            <p><strong>后端端口:</strong> 8000 (固定)</p>
            <p><strong>前端URL:</strong> http://localhost:3000</p>
            <p><strong>后端URL:</strong> http://localhost:8000</p>
        </div>

        <!-- 端口状态检查 -->
        <div class="test-panel">
            <h2>🌐 端口状态检查</h2>
            <div>
                <span>前端服务 (3000):</span>
                <span class="status testing" id="frontend-status">检查中...</span>
            </div>
            <div>
                <span>后端服务 (8000):</span>
                <span class="status testing" id="backend-status">检查中...</span>
            </div>
            <div>
                <span>API代理:</span>
                <span class="status testing" id="proxy-status">检查中...</span>
            </div>
            
            <button class="test-button" onclick="checkAllPorts()">重新检查</button>
        </div>

        <!-- API连接测试 -->
        <div class="test-panel">
            <h2>🔌 API连接测试</h2>
            <button class="test-button" onclick="testHealthAPI()">测试健康检查</button>
            <button class="test-button" onclick="testMarketAPI()">测试市场数据</button>
            <button class="test-button" onclick="testBacktestAPI()">测试回测API</button>
            
            <div class="test-log" id="api-log">
                等待API测试...
            </div>
        </div>

        <!-- 配置验证 -->
        <div class="test-panel">
            <h2>⚙️ 配置验证</h2>
            <div id="config-details">
                <p><strong>当前页面URL:</strong> <span id="current-url"></span></p>
                <p><strong>预期前端端口:</strong> 3000</p>
                <p><strong>预期后端端口:</strong> 8000</p>
            </div>
        </div>
    </div>

    <script>
        let apiLog = document.getElementById('api-log');
        
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const color = type === 'error' ? '#ef4444' : type === 'success' ? '#10b981' : '#3b82f6';
            apiLog.innerHTML += `<div style="color: ${color}">[${timestamp}] ${message}</div>`;
            apiLog.scrollTop = apiLog.scrollHeight;
        }
        
        function updateStatus(elementId, status, text) {
            const element = document.getElementById(elementId);
            element.className = `status ${status}`;
            element.textContent = text;
        }
        
        async function checkAllPorts() {
            log('🔍 开始检查端口状态...', 'info');
            
            // 检查前端端口
            updateStatus('frontend-status', 'testing', '检查中...');
            try {
                const currentPort = window.location.port || (window.location.protocol === 'https:' ? '443' : '80');
                if (currentPort === '3000') {
                    updateStatus('frontend-status', 'success', '✅ 正常 (3000)');
                    log('✅ 前端端口检查通过: 3000', 'success');
                } else {
                    updateStatus('frontend-status', 'error', `❌ 错误 (${currentPort})`);
                    log(`❌ 前端端口错误: 期望3000，实际${currentPort}`, 'error');
                }
            } catch (error) {
                updateStatus('frontend-status', 'error', '❌ 检查失败');
                log(`❌ 前端端口检查失败: ${error.message}`, 'error');
            }
            
            // 检查后端端口
            updateStatus('backend-status', 'testing', '检查中...');
            try {
                const response = await fetch('http://localhost:8000/health');
                if (response.ok) {
                    updateStatus('backend-status', 'success', '✅ 正常 (8000)');
                    log('✅ 后端端口检查通过: 8000', 'success');
                } else {
                    updateStatus('backend-status', 'error', '❌ 响应错误');
                    log(`❌ 后端响应错误: ${response.status}`, 'error');
                }
            } catch (error) {
                updateStatus('backend-status', 'error', '❌ 连接失败');
                log(`❌ 后端连接失败: ${error.message}`, 'error');
            }
            
            // 检查API代理
            updateStatus('proxy-status', 'testing', '检查中...');
            try {
                const response = await fetch('/api/v1/market/overview');
                if (response.ok) {
                    updateStatus('proxy-status', 'success', '✅ 正常');
                    log('✅ API代理检查通过', 'success');
                } else {
                    updateStatus('proxy-status', 'error', '❌ 代理错误');
                    log(`❌ API代理错误: ${response.status}`, 'error');
                }
            } catch (error) {
                updateStatus('proxy-status', 'error', '❌ 代理失败');
                log(`❌ API代理失败: ${error.message}`, 'error');
            }
        }
        
        async function testHealthAPI() {
            log('🏥 测试健康检查API...', 'info');
            try {
                const response = await fetch('/health');
                const data = await response.json();
                log(`✅ 健康检查成功: ${JSON.stringify(data)}`, 'success');
            } catch (error) {
                log(`❌ 健康检查失败: ${error.message}`, 'error');
            }
        }
        
        async function testMarketAPI() {
            log('📈 测试市场数据API...', 'info');
            try {
                const response = await fetch('/api/v1/market/overview');
                const data = await response.json();
                log(`✅ 市场数据获取成功: ${data.data.total_symbols} 个交易对`, 'success');
            } catch (error) {
                log(`❌ 市场数据获取失败: ${error.message}`, 'error');
            }
        }
        
        async function testBacktestAPI() {
            log('🔬 测试回测API...', 'info');
            try {
                const response = await fetch('/api/v1/backtest/tasks');
                const data = await response.json();
                log(`✅ 回测API连接成功: ${data.length} 个任务`, 'success');
            } catch (error) {
                log(`❌ 回测API连接失败: ${error.message}`, 'error');
            }
        }
        
        // 页面加载时初始化
        window.addEventListener('load', () => {
            document.getElementById('current-url').textContent = window.location.href;
            log('🎉 端口配置验证页面加载完成', 'success');
            
            // 自动检查端口状态
            setTimeout(checkAllPorts, 1000);
        });
    </script>
</body>
</html>
