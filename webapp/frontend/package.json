{"name": "coin-analyze-frontend", "version": "1.0.0", "description": "Coin-Analyze 专业加密货币数据分析平台前端", "type": "module", "scripts": {"dev": "vite --port 3000 --strictPort", "dev:host": "vite --port 3000 --host --strictPort", "start": "vite --port 3000 --strictPort", "build": "tsc && vite build", "build:analyze": "npm run build && npx vite-bundle-analyzer dist", "build:optimized": "../scripts/optimize-build.sh", "preview": "vite preview --port 3001", "preview:host": "vite preview --port 3001 --host", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "lint:fix": "eslint . --ext ts,tsx --fix", "type-check": "tsc --noEmit", "clean": "rm -rf dist node_modules/.vite", "preload-check": "node scripts/check-preload.js"}, "dependencies": {"axios": "^1.6.0", "chart.js": "^3.9.0", "clsx": "^1.2.0", "date-fns": "^2.30.0", "framer-motion": "^6.5.0", "lucide-react": "^0.294.0", "numeral": "^2.0.6", "react": "^17.0.2", "react-chartjs-2": "^4.3.0", "react-dom": "^17.0.2", "react-hot-toast": "^2.4.0", "react-query": "^3.39.0", "react-router-dom": "^6.8.0", "recharts": "^2.8.0", "zustand": "^4.4.0"}, "devDependencies": {"@types/node": "^24.1.0", "@types/numeral": "^2.0.5", "@types/react": "^17.0.0", "@types/react-dom": "^17.0.0", "@typescript-eslint/eslint-plugin": "^5.0.0", "@typescript-eslint/parser": "^5.0.0", "@vitejs/plugin-react": "^3.0.0", "autoprefixer": "^10.4.0", "eslint": "^8.45.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.0", "postcss": "^8.4.0", "tailwindcss": "^3.3.0", "terser": "^5.43.1", "typescript": "^4.9.0", "vite": "^4.4.0"}, "keywords": ["cryptocurrency", "trading", "analysis", "react", "typescript", "ai-prediction"], "author": "Coin-Analyze Team", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/your-username/coin-analyze.git"}, "bugs": {"url": "https://github.com/your-username/coin-analyze/issues"}, "homepage": "https://coin-analyze.com"}