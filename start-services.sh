#!/bin/bash

# Coin-Analyze 服务启动脚本
# 前端端口: 3000 (固定)
# 后端端口: 8000 (固定)

echo "🚀 启动 Coin-Analyze 服务..."
echo "📋 配置信息:"
echo "   前端端口: 3000 (固定)"
echo "   后端端口: 8000 (固定)"
echo ""

# 检查端口是否被占用
check_port() {
    local port=$1
    local service=$2
    
    if lsof -i :$port > /dev/null 2>&1; then
        echo "⚠️  端口 $port 已被占用 ($service)"
        echo "   正在尝试释放端口..."
        
        # 获取占用端口的进程ID
        local pids=$(lsof -ti :$port)
        if [ ! -z "$pids" ]; then
            echo "   杀掉进程: $pids"
            kill -9 $pids
            sleep 2
        fi
        
        # 再次检查
        if lsof -i :$port > /dev/null 2>&1; then
            echo "❌ 无法释放端口 $port，请手动处理"
            return 1
        else
            echo "✅ 端口 $port 已释放"
        fi
    else
        echo "✅ 端口 $port 可用 ($service)"
    fi
    return 0
}

# 检查必要的端口
echo "🔍 检查端口状态..."
check_port 3000 "前端服务"
if [ $? -ne 0 ]; then
    exit 1
fi

check_port 8000 "后端服务"
if [ $? -ne 0 ]; then
    exit 1
fi

echo ""

# 启动后端服务
echo "🔧 启动后端服务 (端口 8000)..."
cd webapp/backend
python -m uvicorn main:app --host 0.0.0.0 --port 8000 --reload &
BACKEND_PID=$!
echo "   后端服务 PID: $BACKEND_PID"

# 等待后端启动
echo "   等待后端服务启动..."
sleep 5

# 检查后端是否启动成功
if curl -s http://localhost:8000/health > /dev/null; then
    echo "✅ 后端服务启动成功"
else
    echo "❌ 后端服务启动失败"
    kill $BACKEND_PID 2>/dev/null
    exit 1
fi

# 启动前端服务
echo ""
echo "🎨 启动前端服务 (端口 3000)..."
cd ../frontend
npm run dev &
FRONTEND_PID=$!
echo "   前端服务 PID: $FRONTEND_PID"

# 等待前端启动
echo "   等待前端服务启动..."
sleep 8

# 检查前端是否启动成功
if curl -s http://localhost:3000 > /dev/null; then
    echo "✅ 前端服务启动成功"
else
    echo "❌ 前端服务启动失败"
    kill $FRONTEND_PID 2>/dev/null
    kill $BACKEND_PID 2>/dev/null
    exit 1
fi

echo ""
echo "🎉 所有服务启动成功！"
echo ""
echo "📱 访问地址:"
echo "   前端应用: http://localhost:3000"
echo "   分析工具: http://localhost:3000/analysis"
echo "   端口验证: http://localhost:3000/port-config-test.html"
echo "   后端API:  http://localhost:8000"
echo "   API文档:  http://localhost:8000/docs"
echo ""
echo "🛑 停止服务:"
echo "   按 Ctrl+C 停止脚本"
echo "   或运行: kill $FRONTEND_PID $BACKEND_PID"
echo ""

# 保存PID到文件
echo $FRONTEND_PID > .frontend.pid
echo $BACKEND_PID > .backend.pid

# 等待用户中断
trap 'echo ""; echo "🛑 正在停止服务..."; kill $FRONTEND_PID $BACKEND_PID 2>/dev/null; rm -f .frontend.pid .backend.pid; echo "✅ 服务已停止"; exit 0' INT

echo "⏳ 服务运行中... (按 Ctrl+C 停止)"
wait
