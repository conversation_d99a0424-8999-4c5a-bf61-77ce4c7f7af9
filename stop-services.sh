#!/bin/bash

# Coin-Analyze 服务停止脚本

echo "🛑 停止 Coin-Analyze 服务..."

# 停止通过PID文件记录的服务
if [ -f .frontend.pid ]; then
    FRONTEND_PID=$(cat .frontend.pid)
    echo "🎨 停止前端服务 (PID: $FRONTEND_PID)..."
    kill $FRONTEND_PID 2>/dev/null
    rm -f .frontend.pid
fi

if [ -f .backend.pid ]; then
    BACKEND_PID=$(cat .backend.pid)
    echo "🔧 停止后端服务 (PID: $BACKEND_PID)..."
    kill $BACKEND_PID 2>/dev/null
    rm -f .backend.pid
fi

# 强制停止占用端口的进程
echo "🔍 检查并停止占用端口的进程..."

# 停止3000端口的进程
if lsof -i :3000 > /dev/null 2>&1; then
    echo "   停止占用3000端口的进程..."
    lsof -ti :3000 | xargs kill -9 2>/dev/null
fi

# 停止8000端口的进程
if lsof -i :8000 > /dev/null 2>&1; then
    echo "   停止占用8000端口的进程..."
    lsof -ti :8000 | xargs kill -9 2>/dev/null
fi

# 等待进程完全停止
sleep 2

# 验证端口是否已释放
echo "✅ 验证端口状态:"
if lsof -i :3000 > /dev/null 2>&1; then
    echo "   ⚠️  端口3000仍被占用"
else
    echo "   ✅ 端口3000已释放"
fi

if lsof -i :8000 > /dev/null 2>&1; then
    echo "   ⚠️  端口8000仍被占用"
else
    echo "   ✅ 端口8000已释放"
fi

echo ""
echo "🎉 服务停止完成！"
